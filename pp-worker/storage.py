from abc import ABC, abstractmethod
import os
import requests
from supabase import create_client, Client
import json
import logging # <-- Added import
from config import service_key, url
class StorageInterface(ABC):
    @abstractmethod
    def get_files(self):
        pass

    @abstractmethod
    def put_files(self):
        pass

class SuperbaseStorage(StorageInterface):
    def __init__(self, bucket_name, local_dir='./tmp', result_dir=f"/output" ):
        self.local_dir = local_dir
        self.result_dir = local_dir +  result_dir
        self.bucket_name = bucket_name
        if not os.path.exists(self.local_dir):
            os.makedirs(self.local_dir)
        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)
        self.supabase = create_client(url, service_key)
    """
    def get_files(self, files):
        for file in files:
            url = self.supabase.storage.from_(bucket_name).create_signed_url(file,60)["signedURL"]
            filename = os.path.basename(file)
            response = requests.get(url, stream=True)
            if response.status_code != 200:
                print(f"Failed to download {file}. Status code: {response.status_code}")
                continue
            with open(os.path.join(self.local_dir, filename), 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:  # filter out keep-alive new chunks
                        f.write(chunk)"""
    # Original get_files for standard jobs
    def get_files(self, files, folder):
        """
        Downloads files relative to a folder path from the Supabase bucket.
        Used for standard jobs.

        Args:
            files (list[str]): A list of relative file paths or basenames.
            folder (str): The remote folder path prefix within the bucket.

        Returns:
            list[str]: A list of local paths where files were successfully downloaded.
        """
        downloaded_files = []
        if not files:
            logging.warning("Standard get_files called with empty files list.")
            return downloaded_files

        for file in files: # 'file' here is likely a relative path or basename
            local_file_path = None # Define outside try block for cleanup
            remote_file_path = None
            try:
                # Construct the full remote path
                remote_file_path = f"{folder}/{file}".replace('//', '/') # Basic path joining
                # Handle case where folder might be empty or root '/'
                if remote_file_path.startswith('/'):
                     remote_file_path = remote_file_path.lstrip('/')

                if not remote_file_path:
                    logging.warning(f"Skipping download due to empty remote path derived from folder='{folder}', file='{file}'")
                    continue

                url_response = self.supabase.storage.from_(self.bucket_name).create_signed_url(remote_file_path, 60)

                if "signedURL" not in url_response or not url_response["signedURL"]:
                     logging.error(f"Failed to create signed URL for {remote_file_path}. Response: {url_response}")
                     continue

                signed_url = url_response["signedURL"]
                # Save using basename to the instance's local_dir
                filename = os.path.basename(file) # Use basename of the input 'file'
                local_file_path = os.path.join(self.local_dir, filename)

                logging.info(f"Attempting standard download: {remote_file_path} -> {local_file_path}")
                response = requests.get(signed_url, stream=True)
                response.raise_for_status()

                with open(local_file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                downloaded_files.append(local_file_path)
                logging.info(f"Successfully downloaded {remote_file_path} to {local_file_path}")

            except requests.exceptions.RequestException as e:
                 logging.error(f"HTTP error downloading {remote_file_path or file}: {e}")
            except Exception as e:
                 logging.error(f"Failed to download or save {remote_file_path or file}: {e}", exc_info=True)
                 if local_file_path and os.path.exists(local_file_path):
                     try: os.remove(local_file_path)
                     except OSError as rm_err: logging.error(f"Error cleaning up partial download {local_file_path}: {rm_err}")

        return downloaded_files

    # Renamed method for bulk jobs
    def get_files_bulk(self, files_map: dict[str, str]):
        """
        Downloads files from the Supabase bucket using their full remote paths
        and saves them to specified unique local paths. Used for bulk jobs.

        Args:
            files_map (dict[str, str]): A dictionary mapping full remote file paths
                                        (keys) to desired unique absolute local file paths (values).

        Returns:
            list[str]: A list of local paths where files were successfully downloaded.
        """
        downloaded_files = []
        if not files_map:
            logging.warning("get_files_bulk called with an empty files_map.")
            return downloaded_files

        for remote_file_path, desired_local_path in files_map.items():
            try:
                local_dir = os.path.dirname(desired_local_path)
                if not os.path.exists(local_dir):
                    os.makedirs(local_dir, exist_ok=True)

                url_response = self.supabase.storage.from_(self.bucket_name).create_signed_url(remote_file_path, 60)

                if "signedURL" not in url_response or not url_response["signedURL"]:
                     logging.error(f"Failed to create signed URL for {remote_file_path}. Response: {url_response}")
                     continue

                signed_url = url_response["signedURL"]

                logging.info(f"Attempting bulk download: {remote_file_path} -> {desired_local_path}")
                response = requests.get(signed_url, stream=True)
                response.raise_for_status()

                with open(desired_local_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                downloaded_files.append(desired_local_path)
                logging.info(f"Successfully downloaded {remote_file_path} to {desired_local_path}")

            except requests.exceptions.RequestException as e:
                 logging.error(f"HTTP error downloading {remote_file_path}: {e}")
            except OSError as e:
                 logging.error(f"OS error creating directory or writing file for {desired_local_path}: {e}")
            except Exception as e:
                 logging.error(f"Failed to download or save {remote_file_path} to {desired_local_path}: {e}", exc_info=True)
                 if os.path.exists(desired_local_path):
                     try: os.remove(desired_local_path)
                     except OSError as rm_err: logging.error(f"Error cleaning up partial download {desired_local_path}: {rm_err}")

        return downloaded_files

    def put_files(self, remoteoutputfolder):
        files = os.listdir(self.result_dir)
        output_files = []
        for file in files:
            filename = os.path.basename(file)
            remotefilepath = remoteoutputfolder + filename
            output_files.append(remotefilepath)
            uploadurl= self.supabase.storage.from_(self.bucket_name).create_signed_upload_url(remotefilepath)["signed_url"]
            if os.path.isdir(os.path.join(self.result_dir, file)):
                continue
            with open(os.path.join(self.result_dir, file), 'rb') as f:
                requests.put(uploadurl, data=f)
        return output_files
        

class LocalMountedStorage(StorageInterface):
    def __init__(self, source_dir, local_dir, result_dir=f"/output"):
        self.local_dir = local_dir
        self.result_dir = local_dir +  result_dir
        if not os.path.exists(self.local_dir):
            os.makedirs(self.local_dir)
        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)
        #mount sorucedir to localdir
        if not os.path.ismount(self.local_dir):
            os.system(f"mount --bind {source_dir} {self.local_dir}")
        
    def __del__(self):
        #unmount localdir
        if os.path.ismount(self.local_dir):
            os.system(f"umount {self.local_dir}")


    def get_files(self, files):
        return files

    def put_files(self):
        files = os.listdir(self.result_dir)
        return files
    

class  CopyLocalStorage(StorageInterface):
    def __init__(self, source_dir, local_dir, result_dir=f"/output"):
        self.source_dir = source_dir
        self.local_dir = local_dir
        self.result_dir = local_dir +  result_dir
        if not os.path.exists(self.local_dir):
            os.makedirs(self.local_dir)
        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)
        
    

    def get_files(self, files):
        #copy files from source_dir to local_dir
        for file in files:
            os.makedirs(os.path.dirname(os.path.join(self.local_dir, file)), exist_ok=True)
            os.system(f"cp -r {os.path.join(self.source_dir, file)} {os.path.join(self.local_dir, file)}")
        return files

    def put_files(self, remoteoutputfolder):
        
        #copy files from result_dir to remoteoutputfolder
        os.system(f"cp -r {self.result_dir}/* {remoteoutputfolder}")
        files = os.listdir(remoteoutputfolder)
        return files