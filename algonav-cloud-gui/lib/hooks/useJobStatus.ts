import { useState, useEffect, useRef, useCallback } from 'react';
import { BulkJobStatus } from '@/components/job/BulkActionsToolbar';

interface UseJobStatusOptions {
  interval?: number;
  maxRetries?: number;
  backoffFactor?: number;
  autoDownload?: boolean;
}

/**
 * Hook to manage bulk job status polling with error handling and exponential backoff
 * @param jobId The ID of the job to poll status for
 * @param options Configuration options for polling
 */
export const useJobStatus = (
  jobId: string | null, 
  options: UseJobStatusOptions = {}
) => {
  const {
    interval = 1000,
    maxRetries = 3,
    backoffFactor = 1.5,
    autoDownload = true
  } = options;

  const [status, setStatus] = useState<BulkJobStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const retryCount = useRef(0);
  const timeoutId = useRef<NodeJS.Timeout>();
  const previousStatus = useRef<BulkJobStatus>('idle');

  // Clear any existing polling timeout when component unmounts or jobId changes
  const clearPolling = useCallback(() => {
    if (timeoutId.current) {
      clearTimeout(timeoutId.current);
      timeoutId.current = undefined;
    }
  }, []);

  // Reset the status when jobId changes to null
  useEffect(() => {
    if (jobId === null) {
      setStatus('idle');
      setError(null);
      clearPolling();
    }
  }, [jobId, clearPolling]);

  // Poll job status when jobId is available
  useEffect(() => {
    if (!jobId) return;
    
    const pollStatus = async () => {
      try {
        // Replace with actual API call when implemented
        const response = await fetch(`/api/tasks/${jobId}/status`);
        if (!response.ok) {
          throw new Error(`Failed to fetch job status: ${response.statusText}`);
        }
        
        const jobStatus = await response.json();
        
        if (jobStatus.status === 'complete') {
          setStatus('complete');
          return; // Stop polling
        }
        
        if (jobStatus.status === 'error') {
          setStatus('error');
          setError(jobStatus.error || 'An error occurred during job processing');
          return; // Stop polling
        }

        // Reset retry count on successful poll
        retryCount.current = 0;
        
        // Continue polling if job is still processing
        setStatus('processing');
        
        // Schedule next poll
        const nextInterval = interval;
        timeoutId.current = setTimeout(pollStatus, nextInterval);
      } catch (err) {
        // Increment retry count
        retryCount.current += 1;
        
        if (retryCount.current > maxRetries) {
          setStatus('error');
          setError('Failed to get job status after multiple attempts');
          return; // Stop polling
        }
        
        // Apply exponential backoff
        const nextInterval = interval * Math.pow(backoffFactor, retryCount.current);
        timeoutId.current = setTimeout(pollStatus, nextInterval);
      }
    };

    // Start polling
    setStatus('processing');
    pollStatus();

    // Cleanup on unmount or when jobId changes
    return clearPolling;
  }, [jobId, interval, maxRetries, backoffFactor, clearPolling]);

  // Auto-download results when status changes to complete
  useEffect(() => {
    if (status === 'complete' && previousStatus.current !== 'complete' && autoDownload) {
      downloadResult();
    }
    previousStatus.current = status;
  }, [status, autoDownload]);

  // Function to reset error and retry polling
  const retry = useCallback(() => {
    if (jobId && status === 'error') {
      setError(null);
      retryCount.current = 0;
      setStatus('processing');
      
      const pollStatus = async () => {
        try {
          const response = await fetch(`/api/tasks/${jobId}/status`);
          // Continue with polling logic as above...
          // This is simplified to avoid repetition
          if (!response.ok) {
            throw new Error(`Failed to fetch job status: ${response.statusText}`);
          }
          
          const jobStatus = await response.json();
          
          if (jobStatus.status === 'complete') {
            setStatus('complete');
            return;
          }
          
          if (jobStatus.status === 'error') {
            setStatus('error');
            setError(jobStatus.error || 'An error occurred during job processing');
            return;
          }
          
          setStatus('processing');
          timeoutId.current = setTimeout(pollStatus, interval);
        } catch (err) {
          retryCount.current += 1;
          
          if (retryCount.current > maxRetries) {
            setStatus('error');
            setError('Failed to get job status after multiple attempts');
            return;
          }
          
          const nextInterval = interval * Math.pow(backoffFactor, retryCount.current);
          timeoutId.current = setTimeout(pollStatus, nextInterval);
        }
      };
      
      pollStatus();
    }
  }, [jobId, status, interval, maxRetries, backoffFactor]);

  // Function to download the completed job result
  const downloadResult = useCallback(async () => {
    if (!jobId || status !== 'complete') return;
    
    try {
      // Replace with actual download logic when implemented
      const response = await fetch(`/api/tasks/${jobId}/download`);
      if (!response.ok) {
        throw new Error(`Failed to download job result: ${response.statusText}`);
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      // Use a sensible filename based on job type
      a.download = `multi-kml-${jobId}.kml`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();
    } catch (err) {
      setError('Failed to download job result');
    }
  }, [jobId, status]);

  return {
    status,
    error,
    retry,
    downloadResult
  };
};
