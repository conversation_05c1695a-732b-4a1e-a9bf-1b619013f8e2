export class BulkJobError extends <PERSON><PERSON>r {
  constructor(
    public code: string,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'BulkJobError';
  }

  static unknown(error: any): BulkJobError {
    return new BulkJobError(
      'UNKNOWN_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      error
    );
  }
}

export function isBulkJobError(error: any): error is BulkJobError {
  return error instanceof BulkJobError || 
         (error && error.name === 'BulkJobError') ||
         (error && typeof error.code === 'string' && typeof error.message === 'string');
}
