import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  But<PERSON>,
  Divider,
  CircularProgress,
  Box
} from '@mui/material';
import DownloadIcon from '../common/DownloadIcon';
import KmlIcon from '@mui/icons-material/Place';

export type BulkJobStatus = 'idle' | 'creating' | 'processing' | 'complete' | 'error';

interface BulkActionsToolbarProps {
  selectedIds: number[];
  downloadingFiles: Record<string, boolean>;
  onDownloadSelected: () => void;
  // Bulk job related props
  bulkJobStatus?: BulkJobStatus;
  onCreateMultiKML?: () => void;
  bulkJobError?: string | null;
}

const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({
  selectedIds,
  downloadingFiles,
  onDownloadSelected,
  bulkJobStatus = 'idle',
  onCreateMultiKML,
  bulkJobError
}) => {
  const isDownloading = selectedIds.some(id => downloadingFiles[`task-${id}`]);
  const hasSelections = selectedIds.length > 0;

  // Get appropriate icon and text for the Create Multi-KML button based on status
  const getMultiKMLButtonIcon = () => {
    switch(bulkJobStatus) {
      case 'creating':
      case 'processing':
        return <CircularProgress size={20} />;
      case 'complete':
        return <DownloadIcon fileType="kml" isMulti={true} />;
      case 'error':
        return <KmlIcon color="error" />;
      default:
        return <KmlIcon />;
    }
  };

  const getMultiKMLButtonText = () => {
    switch(bulkJobStatus) {
      case 'creating':
        return 'Creating...';
      case 'processing':
        return 'Processing...';
      case 'complete':
        return 'Download Multi-KML';
      case 'error':
        return 'Retry Multi-KML';
      default:
        return 'Create Multi-KML';
    }
  };

  const isMultiKMLButtonDisabled = 
    !hasSelections || 
    bulkJobStatus === 'creating' || 
    bulkJobStatus === 'processing';

  return (
    <Paper 
      sx={{ 
        mb: 2, 
        p: 1,
        borderRadius: 1,
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' },
        gap: 1,
        alignItems: { xs: 'flex-start', sm: 'center' },
        opacity: hasSelections ? 1 : 0.7,
        bgcolor: hasSelections ? 'action.selected' : 'background.paper',
        transition: 'all 0.2s ease-in-out',
      }}
      elevation={0}
    >
      <Typography variant="body2" sx={{ mr: 1, px: 1 }}>
        {hasSelections 
          ? `${selectedIds.length} ${selectedIds.length === 1 ? 'item' : 'items'} selected` 
          : "Select items to perform bulk actions"}
      </Typography>
      
      <Divider orientation="vertical" flexItem sx={{ display: { xs: 'none', sm: 'block' } }} />
      <Divider sx={{ display: { xs: 'block', sm: 'none' }, width: '100%' }} />
      
      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        <Button
          variant="text"
          size="small"
          disabled={!hasSelections}
          onClick={onDownloadSelected}
          startIcon={
            isDownloading ? 
            <CircularProgress size={20} /> : 
            <DownloadIcon fileType="all" isMulti={true} />
          }
        >
          Download
        </Button>
        
        {onCreateMultiKML && (
          <Button
            variant="text"
            size="small"
            disabled={isMultiKMLButtonDisabled}
            onClick={onCreateMultiKML}
            startIcon={getMultiKMLButtonIcon()}
            color={bulkJobStatus === 'error' ? 'error' : 'primary'}
          >
            {getMultiKMLButtonText()}
          </Button>
        )}
      </Box>
      
      {bulkJobError && bulkJobStatus === 'error' && (
        <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
          {bulkJobError}
        </Typography>
      )}
    </Paper>
  );
};

export default BulkActionsToolbar;
