/**
 * Job Service - Handles all API calls related to jobs and tasks
 */

/**
 * Download a specific file from a task
 */
export const downloadFile = async (taskId: number, resultId: string): Promise<Blob> => {
  const response = await fetch(`/api/tasks/${taskId}/files/${resultId}`);
  if (!response.ok) throw new Error(`Failed to download file: ${response.statusText}`);
  return response.blob();
};

/**
 * Download all files from a task as a zip
 */
export const downloadTaskFiles = async (taskId: number): Promise<Blob> => {
  const response = await fetch(`/api/tasks/${taskId}/download`);
  if (!response.ok) throw new Error(`Failed to download task files: ${response.statusText}`);
  return response.blob();
};

/**
 * Download all files of a specific type from a batch
 */
export const downloadBatchFilesByType = async (jobId: string, fileType: string): Promise<Blob> => {
  const type = fileType === 'csv' ? 'csv_pva' : fileType;
  const response = await fetch(`/api/jobs/${jobId}/download/${type}`);
  if (!response.ok) throw new Error(`Failed to download ${fileType} files: ${response.statusText}`);
  return response.blob();
};

/**
 * Download all files from a batch
 */
export const downloadAllBatchFiles = async (jobId: string): Promise<Blob> => {
  const response = await fetch(`/api/jobs/${jobId}/download`);
  if (!response.ok) throw new Error(`Failed to download all batch files: ${response.statusText}`);
  return response.blob();
};

/**
 * Create a multi-KML bulk job from selected tasks
 * @param taskIds Array of task IDs to include in the bulk job
 * @returns The ID of the created bulk job
 */
export const createMultiKMLBulkJob = async (taskIds: number[], outputFilename: string): Promise<{ id: string }> => { // Add outputFilename parameter
  const response = await fetch('/api/tasks/bulk/kml', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ taskIds, outputFilename }), // Include outputFilename in the body
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to create bulk job: ${response.statusText}`);
  }
  
  return response.json();
};

/**
 * Get the status of a job
 * @param jobId The ID of the job to get status for
 * @returns The current status of the job
 */
export const getJobStatus = async (jobId: string): Promise<{ 
  status: 'creating' | 'processing' | 'complete' | 'error'; 
  error?: string;
}> => {
  const response = await fetch(`/api/tasks/${jobId}/status`);
  
  if (!response.ok) {
    throw new Error(`Failed to get job status: ${response.statusText}`);
  }
  
  return response.json();
};

/**
 * Download the result of a completed bulk job
 * @param jobId The ID of the completed job
 * @returns A blob containing the job result
 */
export const downloadJobResult = async (jobId: string): Promise<Blob> => {
  const response = await fetch(`/api/tasks/${jobId}/download`);
  
  if (!response.ok) {
    throw new Error(`Failed to download job result: ${response.statusText}`);
  }
  
  return response.blob();
};

/**
 * Submit a support request for a task, job, or general inquiry
 */
export const submitSupportRequest = async (
  taskId: number | null, 
  reason: string, 
  additionalInfo: string,
  supportType: 'dataset' | 'job' | 'general' = 'dataset'
): Promise<{ success: boolean; message: string }> => {
  // In a real implementation, this would be an actual API call
  if (supportType === 'dataset' && taskId !== null) {
    console.log('Dataset-specific support request submitted for task:', taskId, {
      reason,
      additionalInfo,
      supportType
    });
  } else if (supportType === 'job') {
    console.log('Job-wide support request submitted:', {
      reason,
      additionalInfo,
      supportType
    });
  } else {
    console.log('General support request submitted:', {
      reason,
      additionalInfo,
      supportType
    });
  }
  
  // Simulate API response
  return {
    success: true,
    message: 'Your support request has been submitted successfully.'
  };
};
